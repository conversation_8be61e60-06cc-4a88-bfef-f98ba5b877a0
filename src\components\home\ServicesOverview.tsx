import React, { useState, useEffect } from "react";
import {
  ChevronLeft,
  ChevronRight,
  Zap,
  Wrench,
  Droplets,
  Flame,
  Shield,
  Battery,
  Sun,
  Settings,
  Database,
  Cog,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface Service {
  id: string;
  title: string;
  description: string;
  points: string[];
  cta: string;
  icon: React.ReactNode;
  image: string;
  gradient: string;
}

const ServicesOverview: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const services: Service[] = [
    {
      id: "electrical",
      title: "Electrical",
      description:
        "Professional electrical installation, maintenance, and repair services for residential and commercial properties with cutting-edge technology solutions.",
      points: [
        "Advanced electrical installation & smart wiring systems",
        "Panel upgrades with digital monitoring capabilities",
        "LED lighting solutions & automated controls",
        "24/7 emergency electrical response team",
        "Comprehensive electrical safety & compliance audits",
      ],
      cta: "Get Electrical Quote",
      icon: <Zap className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1621905251918-48416bd8575a?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "hvac",
      title: "HVAC",
      description:
        "Complete heating, ventilation, and air conditioning solutions for optimal indoor climate control with energy-efficient technologies.",
      points: [
        "Smart AC installation with IoT integration",
        "Preventive heating system maintenance programs",
        "Advanced ductwork design & purification systems",
        "Energy-efficient system upgrades & retrofits",
        "Round-the-clock emergency HVAC support",
      ],
      cta: "Schedule HVAC Service",
      icon: <Wrench className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "plumbing",
      title: "Plumbing",
      description:
        "Expert plumbing solutions for all your water and drainage needs with innovative leak detection and smart water management systems.",
      points: [
        "Advanced pipe installation with leak detection",
        "High-pressure drain cleaning & video inspection",
        "Smart water heater installation & maintenance",
        "Luxury bathroom & kitchen plumbing solutions",
        "Emergency plumbing with rapid response guarantee",
      ],
      cta: "Call Plumber Now",
      icon: <Droplets className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "fire-safety",
      title: "Fire & Safety",
      description:
        "Comprehensive fire protection and safety systems using state-of-the-art technology to keep your property and people safe.",
      points: [
        "Intelligent fire alarm systems with remote monitoring",
        "Advanced sprinkler systems & maintenance programs",
        "Fire extinguisher services & safety training",
        "Emergency lighting & evacuation system design",
        "Comprehensive safety compliance & risk assessments",
      ],
      cta: "Ensure Fire Safety",
      icon: <Shield className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "energy-audit",
      title: "Energy Audit",
      description:
        "Professional energy assessments using advanced analytics to optimize efficiency and dramatically reduce operational costs.",
      points: [
        "Comprehensive energy assessments with thermal imaging",
        "AI-powered efficiency improvement recommendations",
        "Detailed ROI analysis & sustainability roadmaps",
        "Real-time energy usage monitoring systems",
        "Green building certification & sustainability consulting",
      ],
      cta: "Start Energy Audit",
      icon: <Battery className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "data-center",
      title: "Data Center",
      description:
        "Specialized data center infrastructure services for mission-critical operations with redundant systems and 99.9% uptime guarantee.",
      points: [
        "Precision cooling systems for optimal server performance",
        "Redundant power distribution & UPS systems",
        "Structured cabling & high-speed networking solutions",
        "Advanced backup power systems & generators",
        "Environmental monitoring & automated controls",
      ],
      cta: "Optimize Data Center",
      icon: <Database className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "solar",
      title: "Solar Solutions",
      description:
        "Renewable energy solutions with premium solar panel installation and smart energy management for maximum efficiency.",
      points: [
        "High-efficiency solar panel installation & design",
        "Custom solar system planning & optimization",
        "Grid-tie, off-grid & hybrid energy solutions",
        "Preventive maintenance & performance monitoring",
        "Advanced battery storage & energy management systems",
      ],
      cta: "Go Solar Today",
      icon: <Sun className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1509391366360-2e959784a276?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
    {
      id: "ibms",
      title: "IBMS (Intelligent Building Management)",
      description:
        "Smart building automation systems for efficient facility management with AI-powered controls and predictive maintenance.",
      points: [
        "Advanced building automation & IoT integration",
        "Smart lighting with occupancy & daylight sensing",
        "Integrated security & access control systems",
        "Predictive maintenance & energy optimization",
        "Centralized monitoring & mobile app controls",
      ],
      cta: "Smart Building Setup",
      icon: <Settings className="w-6 h-6" />,
      image:
        "https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=600&fit=crop",
      gradient: "from-green-400 to-emerald-400",
    },
  ];

  const nextSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev + 1) % services.length);
    setTimeout(() => setIsAnimating(false), 500);
  };

  const prevSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev - 1 + services.length) % services.length);
    setTimeout(() => setIsAnimating(false), 500);
  };

  const goToSlide = (index: number) => {
    if (isAnimating || index === currentIndex) return;
    setIsAnimating(true);
    setCurrentIndex(index);
    setTimeout(() => setIsAnimating(false), 500);
  };

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 8000);
    return () => clearInterval(interval);
  }, [isAnimating]);

  const currentService = services[currentIndex];

  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-12 bg-white">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center mb-16"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-4"
        >
          <Cog className="w-4 h-4 mr-2" />
          Our Core Services
        </motion.div>

        <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
          Comprehensive MEP Engineering Solutions
        </h2>

        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          From initial concept to final commissioning, we deliver integrated
          mechanical, electrical, and plumbing engineering services that exceed
          industry standards.
        </p>
      </motion.div>

      {/* Services Navigation Bar */}
      <div className="mb-12">
        <div className="flex flex-wrap justify-center gap-3 mb-8">
          {services.map((service, index) => (
            <button
              key={service.id}
              onClick={() => goToSlide(index)}
              className={`group relative flex items-center space-x-3 px-6 py-4 rounded-2xl transition-all duration-300 transform hover:scale-105 ${
                index === currentIndex
                  ? `bg-gradient-to-r ${service.gradient} text-white shadow-2xl`
                  : "bg-white hover:bg-gray-50 text-gray-700 shadow-md hover:shadow-lg border border-gray-200"
              }`}
            >
              <div
                className={`transition-all duration-300 ${
                  index === currentIndex
                    ? "text-white"
                    : "text-gray-500 group-hover:text-gray-700"
                }`}
              >
                {React.cloneElement(service.icon as React.ReactElement, {
                  className:
                    index === currentIndex
                      ? "fill-white stroke-gray-700"
                      : "text-gray-500",
                })}
              </div>
              <span className="font-semibold text-sm whitespace-nowrap">
                {service.title}
              </span>
              {/* {index === currentIndex && (
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full shadow-lg"></div>
              )} */}
            </button>
          ))}
        </div>
      </div>
      {/* <div>
           <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={prevSlide}
          disabled={isAnimating}
          className="absolute left-0 top-1/2 transform -translate-y-1/2 p-4 rounded-full bg-white shadow-xl hover:shadow-2xl text-gray-700 hover:text-gray-900 transition-all duration-300 disabled:opacity-50 z-30 "
        >
          <ChevronLeft className="w-6 h-6" />
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={nextSlide}
          disabled={isAnimating}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 p-4 rounded-full bg-white shadow-xl hover:shadow-2xl text-gray-700 hover:text-gray-900 transition-all duration-300 disabled:opacity-50 z-30"
        >
          <ChevronRight className="w-6 h-6" />
        </motion.button>
      </div> */}
   

      {/* Main Service Card */}
      <div className="relative overflow-hidden bg-white">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ x: 300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -300, opacity: 0 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
              duration: 0.5,
            }}
            className="bg-white rounded-3xl shadow-md overflow-hidden"
          >
            <div className="relative">
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
              <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-50 to-transparent rounded-full opacity-50"></div>

              <div className="relative flex flex-col lg:flex-row min-h-[600px] ">
                {/* Left Side - Service Details */}
                <motion.div
                  initial={{ x: -50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                  className="flex-1 p-12 lg:p-16 flex flex-col justify-center "
                >
                  <div className="mb-8">
                    <motion.div
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: 0.3, duration: 0.5 }}
                      className={`inline-flex items-center space-x-4 px-6 py-3 rounded-2xl bg-gradient-to-r ${currentService.gradient} text-white mb-6 shadow-lg`}
                    >
                      {currentService.icon}
                      <span className="font-bold text-lg">
                        {currentService.title}
                      </span>
                    </motion.div>

                    <motion.p
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.6 }}
                      className="text-gray-700 text-xl leading-relaxed mb-8"
                    >
                      {currentService.description}
                    </motion.p>
                  </div>

                  <div className="mb-10">
                    {/* <motion.h4
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.6 }}
                      className="text-2xl font-bold text-gray-800 mb-6"
                    >
                      Premium Features:
                    </motion.h4> */}
                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.6, duration: 0.6 }}
                      className="space-y-4"
                    >
                      {currentService.points.map((point, index) => (
                        <motion.div
                          key={index}
                          initial={{ x: -20, opacity: 0 }}
                          animate={{ x: 0, opacity: 1 }}
                          transition={{
                            delay: 0.7 + index * 0.1,
                            duration: 0.5,
                          }}
                          className="flex items-start group"
                        >
                          <div
                            className={`w-3 h-3 rounded-full bg-gradient-to-r ${currentService.gradient} mt-2 mr-4 flex-shrink-0 shadow-md group-hover:scale-110 transition-transform duration-200`}
                          ></div>
                          <span className="text-gray-700 text-lg leading-relaxed group-hover:text-gray-900 transition-colors duration-200">
                            {point}
                          </span>
                        </motion.div>
                      ))}
                    </motion.div>
                  </div>

                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.9, duration: 0.6 }}
                    className="flex items-center space-x-4"
                  >
                    <button
                      className={`bg-gradient-to-r ${currentService.gradient} hover:shadow-xl text-white px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105`}
                    >
                      {currentService.cta}
                    </button>
                    <button className="border-2 border-gray-300 hover:border-gray-400 text-gray-700 px-8 py-4 rounded-2xl font-semibold transition-all duration-300 hover:bg-gray-50">
                      Learn More
                    </button>
                  </motion.div>
                </motion.div>

                {/* Right Side - Service Image */}
                <motion.div
                  initial={{ x: 50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                  className="flex-1 relative overflow-hidden"
                >
                  <motion.img
                    initial={{ scale: 1.1 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, duration: 0.8 }}
                    src={currentService.image}
                    alt={currentService.title}
                    className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
                  />
                  {/* Decorative Elements */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent z-10 pointer-events-none rounded-3xl"></div>
                  <div
                    className={`absolute top-8 right-8 w-20 h-20 bg-gradient-to-br ${currentService.gradient} rounded-2xl flex items-center justify-center text-white shadow-lg z-20`}
                  >
                    {currentService.icon}
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Navigation Arrows */}
        
      </div>

      {/* Progress Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1, duration: 0.6 }}
        className="flex justify-center mt-12"
      >
        <div className="flex space-x-2">
          {services.map((_, index) => (
            <motion.div
              key={index}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 1.1 + index * 0.1, duration: 0.3 }}
              className={`h-2 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? `w-8 bg-gradient-to-r ${currentService.gradient}`
                  : "w-2 bg-gray-300"
              }`}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default ServicesOverview;
