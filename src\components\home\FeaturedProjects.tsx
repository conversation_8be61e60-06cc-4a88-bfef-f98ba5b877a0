"use client";

// import React from 'react';
import { motion } from "motion/react";
import {
  ArrowRight,
  MapPin,
  Calendar,
  Users,
  Award,
  ArrowLeft,
} from "lucide-react";
import Image from "next/image";

import React, { useState, useEffect } from "react";

interface Project {
  id: number;
  title: string;
  image: string[];
}

const projects: Project[] = [
  {
    id: 1,
    title: "Textiles",
    image: [
      "/images/projects/anantham-silks-ramnad.jpg",
      "/images/projects/jayachandran-textile-tambaram.jpg",
      "/images/projects/selvarani-textile-alangulam.png",
      "/images/projects/shoba-textile.jpg",
    ],
  },
  {
    id: 2,
    title: "Commercial",
    image: [
      "/images/projects/poorvika.png",
      "/images/projects/pantaloons-1.png",
      "/images/projects/tanishq.jpg",
      "/images/projects/v-five-hotel-bar.png",
    ],
  },
  {
    id: 3,
    title: "Apartments",
    image: [
      "/images/projects/nova-meridian-mogappair.png",
      "/images/projects/tvs-emerald-apartment-at-vengaivasal.png",
      "/images/projects/vgk-builders-chennai.png",
      "/images/projects/lake-dugar.png",
    ],
  },
  {
    id: 4,
    title: "Residential",
    image: [
      "/images/projects/premaltha-vijayakanth-villa.jpg",
      "/images/projects/murray-bungalow-chennai.jpg",
      "/images/projects/maharajan-residence.jpg",
      "/images/projects/prabhu-deva-villa.png",
    ],
  },
  {
    id: 5,
    title: "Data Centers",
    image: [
      "/images/projects/anna-university.png",
      "/images/projects/esds-data-center.png",
      "/images/projects/mrf-data-center-gujarat.png",
      "/images/projects/state-bank-of-india-head-office.png",
    ],
  },
  {
    id: 6,
    title: "Industrial",
    image: [
      "/images/projects/hindustan-warehouse.png",
      "/images/projects/snecma-hal-bangalore.png",
      "/images/projects/ngc-factory-building.png",
      "/images/projects/esr-india-pvt.png",
    ],
  },
];

const FeaturedProjects: React.FC = () => {
  const extendedProjects = [...projects, ...projects, ...projects];
  const INITIAL_INDEX = projects.length;
  const [currentIndex, setCurrentIndex] = useState(INITIAL_INDEX);
  const [isMoving, setIsMoving] = useState(true);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragCurrentX, setDragCurrentX] = useState(0);

  useEffect(() => {
    if (!isMoving || isDragging) return;
    const interval = setInterval(() => {
      setCurrentIndex((prev) => prev + 1);
    }, 3000);
    return () => clearInterval(interval);
  }, [isMoving, isDragging]);

  useEffect(() => {
    if (currentIndex >= projects.length * 2) {
      const timeout = setTimeout(() => {
        setIsMoving(false);
        setCurrentIndex(INITIAL_INDEX);
      }, 500);
      return () => clearTimeout(timeout);
    }
  }, [currentIndex]);

  useEffect(() => {
    if (!isMoving && currentIndex === INITIAL_INDEX) {
      requestAnimationFrame(() => setIsMoving(true));
    }
  }, [isMoving, currentIndex]);

  const nextProject = () => setCurrentIndex((prev) => prev + 1);
  const prevProject = () => setCurrentIndex((prev) => prev - 1);

  const handleStart = (x: number) => {
    setIsDragging(true);
    setIsMoving(false);
    setDragStartX(x);
    setDragCurrentX(x);
  };

  const handleMove = (x: number) => {
    if (isDragging) setDragCurrentX(x);
  };

  const handleEnd = () => {
    const diff = dragCurrentX - dragStartX;
    if (diff < -100) nextProject();
    setIsDragging(false);
    setIsMoving(true);
    setDragStartX(0);
    setDragCurrentX(0);
  };

  const dragOffset = isDragging ? dragCurrentX - dragStartX : 0;

  return (
    <div className="w-full bg-blue-50 text-white overflow-hidden py-6">
      <div className="px-4 md:px-8 pt-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center "
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium mb-4"
          >
            <Award className="w-4 h-4 mr-2" />
            Featured Projects
          </motion.div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Engineering Excellence in Action
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover how we've transformed complex engineering challenges into
            successful realities across diverse industries and project scales.
          </p>
        </motion.div>
      </div>

      <div
        className="relative py-12 overflow-hidden  "
        onMouseDown={(e) => handleStart(e.clientX)}
        onMouseMove={(e) => handleMove(e.clientX)}
        onMouseUp={handleEnd}
        onMouseLeave={handleEnd}
        onTouchStart={(e) => handleStart(e.touches[0].clientX)}
        onTouchMove={(e) => handleMove(e.touches[0].clientX)}
        onTouchEnd={handleEnd}
      >
        <div
          className={`flex items-center h-full ${
            isMoving ? "transition-transform duration-500 ease-out" : ""
          }`}
          style={{
            transform: `translateX(${-currentIndex * 260 + dragOffset}px)`,
            width: `${extendedProjects.length * 260}px`,
          }}
        >
          {extendedProjects.map((project, index) => {
            const actualIndex = index % projects.length;
            // const isMainCard =
            //   Math.floor(index / projects.length) === 3 &&
            //   actualIndex === currentIndex % projects.length;

            return (
              <div
                key={`${project.id}-${index}`}
                className={`flex-shrink-0 mx-2 md:mx-4 transition-all duration-300 `}
              >
                <div
                  className={`relative overflow-hidden rounded-lg shadow-2xl bg-white/5 backdrop-blur-sm `}
                >
                  <div className="relative grid grid-cols-2 grid-rows-2 w-[800px] h-[400px] gap-1 pointer-events-none select-none">
                    <img
                      src={project.image[0]}
                      alt={project.title}
                      className="col-span-1 row-span-1 w-full h-full object-cover pointer-events-none select-none"
                    />
                    <img
                      src={project.image[1]}
                      alt={project.title}
                      className="col-span-1 row-span-1 w-full h-full object-cover pointer-events-none select-none"
                    />
                    <img
                      src={project.image[2]}
                      alt={project.title}
                      className="col-span-1 row-span-1 w-full h-full object-cover pointer-events-none select-none"
                    />
                    <img
                      src={project.image[3]}
                      alt={project.title}
                      className="col-span-1 row-span-1 w-full h-full object-cover pointer-events-none select-none"
                    />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-80" />
                  <div className="absolute bottom-0 left-0 right-0 p-3 md:p-6">
                    <h2 className={`font-bold mb-1 tracking-wide `}>
                      {project.title}
                    </h2>
                  </div>

                  <div className="absolute top-3 right-3 bg-white/20 backdrop-blur-sm rounded-full px-2 py-1 text-[10px] font-semibold">
                    FEATURED
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      {/* <div>
        <div>
          {" "}
          <button
            onClick={prevProject}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 p-4 rounded-full bg-white shadow-xl hover:shadow-2xl text-gray-700 hover:text-gray-900 transition-all duration-300 disabled:opacity-50 z-30"
          >
            <ArrowLeft className="w-6 h-6" />
          </button>
        </div>
        <div>
          {" "}
          <button
            onClick={nextProject}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 p-4 rounded-full bg-white shadow-xl hover:shadow-2xl text-gray-700 hover:text-gray-900 transition-all duration-300 disabled:opacity-50 z-30"
          >
            <ArrowRight className="w-6 h-6" />
          </button>
        </div>
        <div className="flex justify-center mt-3 space-x-2 mb-6">
          {projects.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentIndex % projects.length
                  ? "bg-blue-600 scale-125"
                  : "bg-blue-500 hover:bg-blue-600"
              }`}
            />
          ))}
        </div>
      </div> */}
      <div className="flex justify-center">
        <button className="bg-blue-600 text-white  p-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center group w-fit">
          View Projects
          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
        </button>
      </div>
    </div>
  );
};

export default FeaturedProjects;
