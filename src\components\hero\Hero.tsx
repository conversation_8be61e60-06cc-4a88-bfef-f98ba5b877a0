// "use client";

// import React from 'react';
// import { motion } from 'motion/react';
// import { ArrowRight, Building, Award, Users, CheckCircle } from 'lucide-react';
// import Image from 'next/image';

// const Hero = () => {
//   return (
//     <div className="relative min-h-screen w-full overflow-hidden">
//       {/* Hero Background Image */}
//       <div className="absolute inset-0">
//         <Image
//           src="/images/hero/hero-banner.jpg"
//           alt="Professional MEP Engineering Projects"
//           fill
//           className="object-cover"
//           priority
//         />
//         <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/70 to-slate-900/50"></div>
//         <div className="absolute inset-0 bg-gradient-to-t from-slate-900/60 via-transparent to-transparent"></div>
//       </div>

//       <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
//         <div className="pt-20 pb-16 lg:pt-32 lg:pb-24">
//           {/* Main Hero Content */}
//           <div className="grid lg:grid-cols-2 gap-12 items-center">
//             {/* Left Column - Text Content */}
//             <motion.div
//               initial={{ opacity: 0, x: -50 }}
//               animate={{ opacity: 1, x: 0 }}
//               transition={{ duration: 0.8, delay: 0.2 }}
//               className="space-y-8"
//             >
//               {/* Badge */}
//               <motion.div
//                 initial={{ opacity: 0, y: 20 }}
//                 animate={{ opacity: 1, y: 0 }}
//                 transition={{ duration: 0.6, delay: 0.4 }}
//                 className="inline-flex items-center px-4 py-2 bg-blue-600/20 text-blue-300 rounded-full text-sm font-medium backdrop-blur-sm border border-blue-400/30"
//               >
//                 <Award className="w-4 h-4 mr-2" />
//                 Professional MEP Engineering Excellence
//               </motion.div>

//               {/* Main Headline */}
//               <div className="space-y-6">
//                 <motion.h1
//                   initial={{ opacity: 0, y: 30 }}
//                   animate={{ opacity: 1, y: 0 }}
//                   transition={{ duration: 0.8, delay: 0.6 }}
//                   className="text-4xl lg:text-6xl font-bold text-white leading-tight"
//                 >
//                   Engineering{' '}
//                   <span className="text-blue-400 relative">
//                     Excellence
//                     <motion.div
//                       initial={{ scaleX: 0 }}
//                       animate={{ scaleX: 1 }}
//                       transition={{ duration: 0.8, delay: 1.2 }}
//                       className="absolute bottom-2 left-0 right-0 h-3 bg-blue-400/30 -z-10 origin-left"
//                     />
//                   </span>{' '}
//                   for Modern Infrastructure
//                 </motion.h1>

//                 <motion.p
//                   initial={{ opacity: 0, y: 20 }}
//                   animate={{ opacity: 1, y: 0 }}
//                   transition={{ duration: 0.6, delay: 0.8 }}
//                   className="text-xl text-gray-300 leading-relaxed max-w-2xl"
//                 >
//                   JS Consultants delivers comprehensive MEP engineering solutions with precision, innovation, and unwavering commitment to quality. From concept to completion, we transform complex engineering challenges into successful realities.
//                 </motion.p>
//               </div>

//               {/* CTA Buttons */}
//               <motion.div
//                 initial={{ opacity: 0, y: 20 }}
//                 animate={{ opacity: 1, y: 0 }}
//                 transition={{ duration: 0.6, delay: 1.0 }}
//                 className="flex flex-col sm:flex-row gap-4"
//               >
//                 <motion.button
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                   className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-700 transition-all duration-300 backdrop-blur-sm"
//                 >
//                   Start Your Project
//                   <ArrowRight className="ml-2 w-5 h-5" />
//                 </motion.button>

//                 <motion.button
//                   whileHover={{ scale: 1.05 }}
//                   whileTap={{ scale: 0.95 }}
//                   className="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-lg hover:border-blue-400 hover:text-blue-400 transition-all duration-300 backdrop-blur-sm"
//                 >
//                   View Portfolio
//                 </motion.button>
//               </motion.div>

//               {/* Key Features */}
//               <motion.div
//                 initial={{ opacity: 0, y: 20 }}
//                 animate={{ opacity: 1, y: 0 }}
//                 transition={{ duration: 0.6, delay: 1.2 }}
//                 className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8"
//               >
//                 <div className="flex items-center space-x-3">
//                   <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
//                   <span className="text-gray-300 text-sm">Licensed Professional Engineers</span>
//                 </div>
//                 <div className="flex items-center space-x-3">
//                   <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
//                   <span className="text-gray-300 text-sm">15+ Years Experience</span>
//                 </div>
//                 <div className="flex items-center space-x-3">
//                   <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
//                   <span className="text-gray-300 text-sm">100+ Projects Delivered</span>
//                 </div>
//               </motion.div>
//             </motion.div>

//             {/* Right Column - Professional Image */}
//             <motion.div
//               initial={{ opacity: 0, x: 50 }}
//               animate={{ opacity: 1, x: 0 }}
//               transition={{ duration: 0.8, delay: 0.4 }}
//               className="relative"
//             >
//               <div className="relative">
//                 <motion.div
//                   initial={{ opacity: 0, scale: 0.8 }}
//                   animate={{ opacity: 1, scale: 1 }}
//                   transition={{ duration: 1, delay: 0.6 }}
//                   className="relative rounded-2xl overflow-hidden shadow-2xl"
//                 >
//                   <Image
//                     src="/images/hero/hero-secondary.jpg"
//                     alt="Professional MEP Engineering Team at Work"
//                     width={600}
//                     height={400}
//                     className="object-cover"
//                   />
//                   <div className="absolute inset-0 bg-gradient-to-t from-slate-900/40 to-transparent"></div>
//                 </motion.div>

//                 {/* Floating Stats Cards */}
//                 <motion.div
//                   initial={{ opacity: 0, y: 20 }}
//                   animate={{ opacity: 1, y: 0 }}
//                   transition={{ duration: 0.6, delay: 1.0 }}
//                   className="absolute -bottom-6 -left-6 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20"
//                 >
//                   <div className="flex items-center space-x-3">
//                     <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
//                       <Building className="w-6 h-6 text-blue-600" />
//                     </div>
//                     <div>
//                       <div className="text-2xl font-bold text-gray-900">500+</div>
//                       <div className="text-sm text-gray-600">Projects Completed</div>
//                     </div>
//                   </div>
//                 </motion.div>

//                 <motion.div
//                   initial={{ opacity: 0, y: 20 }}
//                   animate={{ opacity: 1, y: 0 }}
//                   transition={{ duration: 0.6, delay: 1.2 }}
//                   className="absolute -top-6 -right-6 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20"
//                 >
//                   <div className="flex items-center space-x-3">
//                     <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
//                       <Users className="w-6 h-6 text-green-600" />
//                     </div>
//                     <div>
//                       <div className="text-2xl font-bold text-gray-900">25+</div>
//                       <div className="text-sm text-gray-600">Expert Engineers</div>
//                     </div>
//                   </div>
//                 </motion.div>
//               </div>
//             </motion.div>
//           </div>
//         </div>
//       </div>

//       {/* Floating Elements */}
//       <motion.div
//         animate={{
//           y: [0, -20, 0],
//           rotate: [0, 5, 0]
//         }}
//         transition={{
//           duration: 6,
//           repeat: Infinity,
//           ease: "easeInOut"
//         }}
//         className="absolute top-1/4 right-10 w-16 h-16 bg-blue-600/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-60 border border-blue-400/30"
//       >
//         <Award className="w-8 h-8 text-blue-400" />
//       </motion.div>

//       <motion.div
//         animate={{
//           y: [0, 15, 0],
//           rotate: [0, -3, 0]
//         }}
//         transition={{
//           duration: 8,
//           repeat: Infinity,
//           ease: "easeInOut",
//           delay: 2
//         }}
//         className="absolute bottom-1/4 left-10 w-12 h-12 bg-green-600/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-60 border border-green-400/30"
//       >
//         <Building className="w-6 h-6 text-green-400" />
//       </motion.div>

//       {/* Scroll Indicator */}
//       <motion.div
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         transition={{ delay: 2 }}
//         className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
//       >
//         <motion.div
//           animate={{ y: [0, 10, 0] }}
//           transition={{ duration: 2, repeat: Infinity }}
//           className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center"
//         >
//           <motion.div
//             animate={{ y: [0, 12, 0] }}
//             transition={{ duration: 2, repeat: Infinity }}
//             className="w-1 h-3 bg-white/60 rounded-full mt-2"
//           />
//         </motion.div>
//       </motion.div>
//     </div>
//   );
// };

// export default Hero;

"use client";

import React from "react";
import { motion } from "motion/react";
import {
  ArrowRight,
  Zap,
  Wind,
  Shield,
  Droplets,
  Sun,
  Building,
} from "lucide-react";
import ServiceCarousel from "./ServiceCarousel";
import LineSVG from "@/assets/svg/LineSVG";

const Hero = () => {
  return (
    <div className="relative  w-full bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden  border-b border-gray-200">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundRepeat: "repeat",
          }}
        ></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto pl-4 sm:pl-6 lg:pl-8 ">
        <div className="pt-5 lg:pt-10">
          {/* Main Hero Content */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Text Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-8"
            >
              {/* Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
              >
                <Zap className="w-4 h-4 mr-2" />
                Electrical & MEP Engineering Excellence
              </motion.div>

              {/* Main Headline */}
              <div className="space-y-4">
                <motion.h1
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight"
                >
                  Transforming Your{" "}
                  <span className="text-blue-600 relative">
                    Dream Projects
                    <LineSVG
                      className="absolute bottom-1 left-0 right-0   -z-10 origin-left "
                      width="100%"
                    />
                  </span>{" "}
                  Into Reality
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  className="text-xl text-gray-600 leading-relaxed max-w-2xl"
                >
                  JS Consultants is a specialized electrical and MEP engineering
                  firm committed to delivering the finest in design and
                  execution for your most ambitious projects.
                </motion.p>
              </div>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.0 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
                >
                  Enquire Now
                  <ArrowRight className="ml-2 w-5 h-5" />
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-lg hover:border-blue-600 hover:text-blue-600 transition-colors"
                >
                  View Our Work
                </motion.button>
              </motion.div>

              {/* Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.2 }}
                className="grid grid-cols-3 gap-8 pt-8 border-t border-gray-200"
              >
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">10+</div>
                  <div className="text-sm text-gray-600">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">150+</div>
                  <div className="text-sm text-gray-600">
                    Projects Completed
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">8</div>
                  <div className="text-sm text-gray-600">Core Services</div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Column - Service Highlights */}
            <motion.div
              initial={{ opacity: 0, y: 100 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="w-full h-full min-w-[650px] 2xl:min-w-[800px]"
            >
              <video
                src={"/output6.webm"}
                autoPlay
                loop
                muted
                playsInline
                className="w-full h-full object-cover"
              />
            </motion.div>
          </div>
        </div>
      </div>

      {/* Floating Elements */}
      <motion.div
        animate={{
          y: [0, -20, 0],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="absolute z-50 top-1/4 right-10 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center opacity-60"
      >
        <Zap className="w-8 h-8 text-blue-600" />
      </motion.div>

      <motion.div
        animate={{
          y: [0, 15, 0],
          rotate: [0, -3, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
        className="absolute bottom-1/4 left-10 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center opacity-60"
      >
        <Building className="w-6 h-6 text-green-600" />
      </motion.div>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-blue-700/30 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-blue-700/60 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Hero;
