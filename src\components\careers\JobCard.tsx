'use client'

import { motion } from 'motion/react'
import { MapPin, Clock, Briefcase, Users, ChevronRight } from 'lucide-react'

interface Job {
  id: number
  title: string
  department: string
  location: string
  type: string
  experience: string
  description: string
  requirements: string[]
}

interface JobCardProps {
  job: Job
}

export function JobCard({ job }: JobCardProps) {
  return (
    <motion.div
      whileHover={{ y: -5, scale: 1.02 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-slate-200"
    >
      {/* Header */}
      <div className="mb-4">
        <h3 className="text-xl font-bold text-slate-800 mb-2">
          {job.title}
        </h3>
        <div className="flex flex-wrap gap-2 mb-3">
          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
            {job.department}
          </span>
          <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
            {job.type}
          </span>
        </div>
      </div>

      {/* Job Details */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center text-slate-600 text-sm">
          <MapPin className="w-4 h-4 mr-2" />
          {job.location}
        </div>
        <div className="flex items-center text-slate-600 text-sm">
          <Clock className="w-4 h-4 mr-2" />
          {job.experience} experience
        </div>
        <div className="flex items-center text-slate-600 text-sm">
          <Briefcase className="w-4 h-4 mr-2" />
          {job.type}
        </div>
      </div>

      {/* Description */}
      <div className="mb-4">
        <p className="text-slate-700 leading-relaxed">
          {job.description}
        </p>
      </div>

      {/* Requirements Preview */}
      <div className="mb-6">
        <h4 className="font-semibold text-slate-800 mb-2">Key Requirements:</h4>
        <ul className="space-y-1">
          {job.requirements.slice(0, 3).map((requirement, index) => (
            <li key={index} className="text-slate-600 text-sm flex items-start">
              <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"></span>
              {requirement}
            </li>
          ))}
          {job.requirements.length > 3 && (
            <li className="text-slate-500 text-sm italic">
              +{job.requirements.length - 3} more requirements
            </li>
          )}
        </ul>
      </div>

      {/* Apply Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center group"
      >
        Apply Now
        <ChevronRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
      </motion.button>
    </motion.div>
  )
}
