"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Zap, Wind, Shield, Droplets, Sun, Building, ChevronLeft, ChevronRight } from 'lucide-react';

const services = [
  {
    id: 1,
    title: "Electrical",
    description: "Complete electrical design and installation services for residential, commercial, and industrial projects.",
    icon: Zap,
    color: "from-blue-500 to-blue-600",
    features: ["Power Distribution", "Lighting Design", "Control Systems"]
  },
  {
    id: 2,
    title: "HVAC Systems",
    description: "Advanced heating, ventilation, and air conditioning solutions for optimal comfort and efficiency.",
    icon: Wind,
    color: "from-green-500 to-green-600",
    features: ["Climate Control", "Energy Efficiency", "Air Quality"]
  },
  {
    id: 3,
    title: "Fire Fighting Systems",
    description: "Comprehensive fire safety and protection systems to safeguard your property and personnel.",
    icon: Shield,
    color: "from-red-500 to-red-600",
    features: ["Fire Detection", "Suppression Systems", "Safety Compliance"]
  },
  {
    id: 4,
    title: "Plumbing",
    description: "Professional plumbing services including design, installation, and maintenance solutions.",
    icon: Droplets,
    color: "from-cyan-500 to-cyan-600",
    features: ["Water Systems", "Drainage", "Maintenance"]
  },
  {
    id: 5,
    title: "Solar Power",
    description: "Sustainable solar energy solutions for reduced costs and environmental impact.",
    icon: Sun,
    color: "from-yellow-500 to-yellow-600",
    features: ["Solar Panels", "Energy Storage", "Grid Integration"]
  },
  {
    id: 6,
    title: "IBMS",
    description: "Intelligent Building Management Systems for automated control and monitoring.",
    icon: Building,
    color: "from-purple-500 to-purple-600",
    features: ["Automation", "Monitoring", "Integration"]
  }
];

const ServiceCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-advance carousel
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % services.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % services.length);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + services.length) % services.length);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  const currentService = services[currentIndex];
  const IconComponent = currentService.icon;

  return (
    <div className="relative w-full max-w-lg mx-auto">
      {/* Main Card */}
      <motion.div
        className="relative bg-white rounded-2xl shadow-2xl overflow-hidden"
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.3 }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5 }}
            className="p-8"
          >
            {/* Icon and Title */}
            <div className="flex items-center space-x-4 mb-6">
              <motion.div
                className={`w-16 h-16 rounded-xl bg-gradient-to-br ${currentService.color} flex items-center justify-center`}
                whileHover={{ rotate: 5, scale: 1.1 }}
                transition={{ duration: 0.3 }}
              >
                <IconComponent className="w-8 h-8 text-white" />
              </motion.div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900">{currentService.title}</h3>
                <div className="w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2"></div>
              </div>
            </div>

            {/* Description */}
            <p className="text-gray-600 mb-6 leading-relaxed">
              {currentService.description}
            </p>

            {/* Features */}
            <div className="space-y-3">
              {currentService.features.map((feature, index) => (
                <motion.div
                  key={feature}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-center space-x-3"
                >
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700 font-medium">{feature}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Navigation Arrows */}
        <button
          onClick={prevSlide}
          className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/80 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110"
        >
          <ChevronLeft className="w-5 h-5 text-gray-600" />
        </button>
        
        <button
          onClick={nextSlide}
          className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/80 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-110"
        >
          <ChevronRight className="w-5 h-5 text-gray-600" />
        </button>
      </motion.div>

      {/* Dots Indicator */}
      <div className="flex justify-center space-x-2 mt-6">
        {services.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentIndex 
                ? 'bg-blue-600 scale-125' 
                : 'bg-gray-300 hover:bg-gray-400'
            }`}
          />
        ))}
      </div>

      {/* Progress Bar */}
      <div className="mt-4 w-full bg-gray-200 rounded-full h-1 overflow-hidden">
        <motion.div
          className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
          initial={{ width: "0%" }}
          animate={{ width: isAutoPlaying ? "100%" : "0%" }}
          transition={{ duration: 4, ease: "linear" }}
          key={currentIndex}
        />
      </div>
    </div>
  );
};

export default ServiceCarousel;
